import { Slot} from "expo-router";
import { tokenCache } from '@clerk/clerk-expo/token-cache'
import { Clerk<PERSON>rov<PERSON> } from "@clerk/clerk-expo";
import { SafeAreaView } from 'react-native-safe-area-context'

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY || 'pk_test_c3dlZXBpbmctcGVyY2gtMjAuY2xlcmsuYWNjb3VudHMuZGV2JA'

console.log('Environment variable:', process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY)
console.log('Publishable key:', publishableKey)

if (!publishableKey) {
  throw new Error(
    'Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env'
  )
}

export default function RootLayout() {
  return (
    <ClerkProvider tokenCache={tokenCache}>
      <SafeAreaView style={{ flex: 1 }}>
         <Slot />
      </SafeAreaView>
     
    </ClerkProvider>
  );
}
