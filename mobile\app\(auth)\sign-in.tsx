import {
  View,
  Text,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
  TextInput,
} from "react-native";
import { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useSignIn } from "@clerk/clerk-expo";
import { authStyles } from "../../assets/styles/auth.styles";
import { COLORS } from "../../constants/colors";

const SignInScreen = () => {
  const router = useRouter();
  const { signIn, setActive, isLoaded } = useSignIn();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setshowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const handleSignIn = async () => {
    if (!email || !password) {
      Alert.alert("Please enter email and password");
      return;
    }
    if (!loading) return;

    setLoading(true);
    try {
      const signInAttempt = await signIn.create({
        identifier: email,
        password,
      });
      if (signInAttempt.status === "complete") {
        await setActive({ session: signInAttempt.createdSessionId });
        console.log(
          "Sign in successful",
          JSON.stringify(signInAttempt, null, 2)
        );
        // router.replace("/");
      } else {
        Alert.alert("Error", "Sign in failed. Please try again");
      }
    } catch (err) {
      Alert.alert("Error", err.message || "Sign in failed.");
      console.log(JSON.stringify(signInAttempt, null, 2));
    } finally {
      setLoading(false);
    }
  };
  return (
    <SafeAreaView style={authStyles.container}>
      <KeyboardAvoidingView
        style={authStyles.keyboardView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView contentContainerStyle={authStyles.scrollContent}>
          <View style={authStyles.imageContainer}>
            <Image
              source={require("../../assets/images/i1.png")}
              style={authStyles.image}
              con
            />
            <Text style={authStyles.title}>Welcome Back</Text>
            {/* FORM CONTAINER */}
            <View style={authStyles.formContainer}>
              <View>
                <TextInput 
                  style={authStyles.textInput}
                  value={email}
                  placeholder="Enter email"
                  placeholderTextColor={COLORS.textLight}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignInScreen;
